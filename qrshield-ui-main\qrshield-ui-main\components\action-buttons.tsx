"use client"

import { <PERSON><PERSON>, ExternalLink, Flag } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { useToast } from "@/hooks/use-toast"

interface ActionButtonsProps {
  url: string
}

export function ActionButtons({ url }: ActionButtonsProps) {
  const { toast } = useToast()

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(url)
      toast({
        title: "Link copiado!",
        description: "O link foi copiado para a área de transferência.",
      })
    } catch (error) {
      toast({
        title: "Erro ao copiar",
        description: "Não foi possível copiar o link.",
        variant: "destructive",
      })
    }
  }

  const handleOpen = () => {
    window.open(url, "_blank", "noopener,noreferrer")
  }

  const handleReport = () => {
    toast({
      title: "Obrigado pela denúncia",
      description: "Sua denúncia foi enviada para análise.",
    })
  }

  return (
    <div className="space-y-3">
      <Button variant="outline" className="w-full bg-transparent" onClick={handleCopy}>
        <Copy className="h-4 w-4 mr-2" />
        Copiar
      </Button>

      <Button variant="destructive" className="w-full" onClick={handleOpen}>
        <ExternalLink className="h-4 w-4 mr-2" />
        Abrir assim mesmo
      </Button>

      <Button
        variant="ghost"
        className="w-full text-green-600 hover:text-green-700 hover:bg-green-50"
        onClick={handleReport}
      >
        <Flag className="h-4 w-4 mr-2" />
        Denunciar
      </Button>
    </div>
  )
}
