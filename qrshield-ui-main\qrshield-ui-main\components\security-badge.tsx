import { Shield, ShieldAlert } from "lucide-react"
import { cn } from "@/lib/utils"

interface SecurityBadgeProps {
  score: number
  isSecure: boolean
}

export function SecurityBadge({ score, isSecure }: SecurityBadgeProps) {
  return (
    <div className="flex justify-center">
      <div
        className={cn(
          "inline-flex items-center gap-2 px-4 py-2 rounded-full font-medium",
          isSecure
            ? "bg-green-100 text-green-800 border border-green-200"
            : "bg-red-100 text-red-800 border border-red-200",
        )}
      >
        {isSecure ? <Shield className="h-4 w-4" /> : <ShieldAlert className="h-4 w-4" />}
        <span>{isSecure ? "Seguro" : "Arriscado"}</span>
        <span className="bg-white px-2 py-1 rounded-full text-xs font-bold">{score}</span>
      </div>
    </div>
  )
}
