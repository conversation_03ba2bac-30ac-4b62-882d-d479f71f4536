import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_mlkit_barcode_scanning/google_mlkit_barcode_scanning.dart';
import 'package:image_picker/image_picker.dart';

/// Service for scanning QR codes from images
class ImageQRScanner {
  static final _barcodeScanner = BarcodeScanner();
  static final _imagePicker = ImagePicker();

  /// Pick an image from gallery and scan for QR codes
  static Future<String?> scanFromGallery() async {
    try {
      if (kIsWeb) {
        // For web, show a message that this feature is not available
        throw Exception('Seleção de imagem da galeria não disponível na web. Use um dispositivo móvel ou teste com QR codes manuais.');
      }

      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );

      if (image == null) return null;

      return await _scanImageFile(image);
    } catch (e) {
      throw Exception('Erro ao selecionar imagem: $e');
    }
  }

  /// Pick an image from camera and scan for QR codes
  static Future<String?> scanFromCamera() async {
    try {
      if (kIsWeb) {
        // For web, show a message that this feature is not available
        throw Exception('Captura de imagem da câmera não disponível na web. Use um dispositivo móvel ou teste com QR codes manuais.');
      }

      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );

      if (image == null) return null;

      return await _scanImageFile(image);
    } catch (e) {
      throw Exception('Erro ao capturar imagem: $e');
    }
  }

  /// Scan QR code from image file
  static Future<String?> _scanImageFile(XFile imageFile) async {
    try {
      final InputImage inputImage;
      
      if (kIsWeb) {
        // For web, read as bytes
        final bytes = await imageFile.readAsBytes();
        inputImage = InputImage.fromBytes(
          bytes: bytes,
          metadata: InputImageMetadata(
            size: Size(800, 600), // Default size for web
            rotation: InputImageRotation.rotation0deg,
            format: InputImageFormat.nv21,
            bytesPerRow: 800,
          ),
        );
      } else {
        // For mobile platforms
        inputImage = InputImage.fromFilePath(imageFile.path);
      }

      final List<Barcode> barcodes = await _barcodeScanner.processImage(inputImage);

      if (barcodes.isEmpty) {
        throw Exception('Nenhum QR Code encontrado na imagem');
      }

      // Return the first QR code found
      final barcode = barcodes.first;
      if (barcode.rawValue == null || barcode.rawValue!.isEmpty) {
        throw Exception('QR Code encontrado mas não foi possível ler o conteúdo');
      }

      return barcode.rawValue;
    } catch (e) {
      if (e.toString().contains('Nenhum QR Code encontrado') || 
          e.toString().contains('não foi possível ler')) {
        rethrow;
      }
      throw Exception('Erro ao processar imagem: $e');
    }
  }

  /// Check if image picker is available
  static Future<bool> isAvailable() async {
    try {
      // For web, image picker has limitations
      if (kIsWeb) {
        return false; // Disabled for web due to limitations
      }
      // image_picker should work on mobile platforms
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get supported image formats
  static List<String> getSupportedFormats() {
    return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
  }

  /// Validate image file before processing
  static bool isValidImageFile(String fileName) {
    final extension = fileName.toLowerCase().split('.').last;
    return getSupportedFormats().contains(extension);
  }

  /// Dispose resources
  static void dispose() {
    _barcodeScanner.close();
  }
}
