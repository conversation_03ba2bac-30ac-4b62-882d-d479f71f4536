import 'dart:async';
import 'package:flutter/foundation.dart';

/// Web QR Scanner service - simplified for cross-platform compatibility
class WebQRScanner {
  static const _timeout = Duration(seconds: 30);

  /// Check if QR scanner is available in the browser
  static bool get isAvailable {
    // Only available on web platform
    return kIsWeb;
  }

  /// Start QR code scanning
  /// Returns the scanned QR code content or throws an exception
  static Future<String> startScanning() async {
    if (!kIsWeb) {
      throw Exception('QR Scanner only available on web platform');
    }

    // For web, we need to implement a proper camera-based QR scanner
    // For now, we'll throw an exception to force the use of manual input
    throw Exception('Câmera QR não disponível na web. Use "Enviar imagem" ou digite manualmente.');
  }

  /// Stop QR code scanning
  static void stopScanning() {
    // No-op for non-web platforms
  }

  /// Check if currently scanning
  static bool get isScanning {
    return false;
  }

  /// Initialize QR scanner
  static bool initialize() {
    // No-op for non-web platforms
    return kIsWeb;
  }
}
