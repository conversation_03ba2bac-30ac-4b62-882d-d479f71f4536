import { ResultsHeader } from "@/components/results-header"
import { SecurityBadge } from "@/components/security-badge"
import { ScanResults } from "@/components/scan-results"
import { SecurityExplanation } from "@/components/security-explanation"
import { ActionButtons } from "@/components/action-buttons"

export default function ResultsPage() {
  // Mock data - in real app this would come from scan results
  const scanData = {
    type: "Link da Web",
    url: "https://bit.ly/3AbCdEf",
    domain: "bit.ly",
    path: "/3AbCdEf",
    securityScore: 10,
    isSecure: true,
  }

  return (
    <div className="min-h-screen bg-background">
      <ResultsHeader />
      <main className="container mx-auto px-4 py-6 max-w-md space-y-6">
        <SecurityBadge score={scanData.securityScore} isSecure={scanData.isSecure} />
        <ScanResults data={scanData} />
        <SecurityExplanation isSecure={scanData.isSecure} />
        <ActionButtons url={scanData.url} />
      </main>
    </div>
  )
}
