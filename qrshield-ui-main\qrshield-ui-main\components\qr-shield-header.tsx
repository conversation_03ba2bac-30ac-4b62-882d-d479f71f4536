import { Settings } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

export function QRShieldHeader() {
  return (
    <header className="flex items-center justify-between p-4 bg-background border-b border-border">
      <h1 className="text-xl font-serif font-black text-foreground">QRShield</h1>
      <Button variant="ghost" size="icon" className="text-muted-foreground hover:text-foreground">
        <Settings className="h-5 w-5" />
        <span className="sr-only">Configurações</span>
      </Button>
    </header>
  )
}
