@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --background: #ffffff;
  --foreground: #1f2937;
  --card: #f8fafc;
  --card-foreground: #374151;
  --popover: #ffffff;
  --popover-foreground: #1f2937;
  --primary: #374151;
  --primary-foreground: #ffffff;
  --secondary: #6366f1;
  --secondary-foreground: #ffffff;
  --muted: #f8fafc;
  --muted-foreground: #1f2937;
  --accent: #6366f1;
  --accent-foreground: #ffffff;
  --destructive: #d97706;
  --destructive-foreground: #ffffff;
  --border: #d1d5db;
  --input: #f8fafc;
  --ring: #6366f1;
  --chart-1: #6366f1;
  --chart-2: #d97706;
  --chart-3: #a16207;
  --chart-4: #4b5563;
  --chart-5: #ffffff;
  --radius: 0.5rem;
  --sidebar: #ffffff;
  --sidebar-foreground: #1f2937;
  --sidebar-primary: #6366f1;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #d97706;
  --sidebar-accent-foreground: #ffffff;
  --sidebar-border: #d1d5db;
  --sidebar-ring: #6366f1;
}

.dark {
  --background: #0f172a;
  --foreground: #f1f5f9;
  --card: #1e293b;
  --card-foreground: #cbd5e1;
  --popover: #1e293b;
  --popover-foreground: #f1f5f9;
  --primary: #f1f5f9;
  --primary-foreground: #0f172a;
  --secondary: #6366f1;
  --secondary-foreground: #ffffff;
  --muted: #334155;
  --muted-foreground: #94a3b8;
  --accent: #6366f1;
  --accent-foreground: #ffffff;
  --destructive: #d97706;
  --destructive-foreground: #ffffff;
  --border: #334155;
  --input: #334155;
  --ring: #6366f1;
  --chart-1: #6366f1;
  --chart-2: #d97706;
  --chart-3: #a16207;
  --chart-4: #64748b;
  --chart-5: #f1f5f9;
  --sidebar: #1e293b;
  --sidebar-foreground: #f1f5f9;
  --sidebar-primary: #6366f1;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #d97706;
  --sidebar-accent-foreground: #ffffff;
  --sidebar-border: #334155;
  --sidebar-ring: #6366f1;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --font-sans: var(--font-open-sans);
  --font-serif: var(--font-montserrat);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom animations for QR scanner */
@keyframes pulse-ring {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.1);
    opacity: 0;
  }
}

@keyframes scan-line {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(100%);
  }
}

.pulse-ring {
  animation: pulse-ring 2s cubic-bezier(0.455, 0.03, 0.515, 0.955) infinite;
}

.scan-line {
  animation: scan-line 2s linear infinite;
}
