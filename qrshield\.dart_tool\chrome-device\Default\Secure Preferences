{"browser": {"show_home_button": true}, "edge": {"services": {"account_id": "00034001BE388EAB", "last_username": "<EMAIL>"}}, "edge_fundamentals_appdefaults": {"enclave_version": 101}, "ess_kv_states": {"restore_on_startup": {"closed_notification": false, "decrypt_success": true, "key": "restore_on_startup", "notification_popup_count": 0}, "startup_urls": {"closed_notification": false, "decrypt_success": true, "key": "startup_urls", "notification_popup_count": 0}, "template_url_data": {"closed_notification": false, "decrypt_success": true, "key": "template_url_data", "notification_popup_count": 0}}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "Des<PERSON>bra as extensões do Microsoft Edge.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Web Store", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\140.0.3485.14\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "bmgogkgklcedkeplmalbcklcbpgelpdg": {"disable_reasons": [512]}, "cjneempfhkonkkbcmnfdibgobmhbagaj": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "ckcbfnpodigdcbjjmhmolhkhlfbepnca": {"disable_reasons": [1]}, "dcaajljecejllikfgbhjdgeognacjkkp": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "dgiklkfkllikcanfonkcabmbdfmgleag": {"account_extension_type": 0, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_capabilities": {"include_globs": ["https://*excel.officeapps.live.com/*", "https://*onenote.officeapps.live.com/*", "https://*powerpoint.officeapps.live.com/*", "https://*word-edit.officeapps.live.com/*", "https://*excel.officeapps.live.com.mcas.ms/*", "https://*onenote.officeapps.live.com.mcas.ms/*", "https://*word-edit.officeapps.live.com.mcas.ms/*", "https://*excel.partner.officewebapps.cn/*", "https://*onenote.partner.officewebapps.cn/*", "https://*powerpoint.partner.officewebapps.cn/*", "https://*word-edit.partner.officewebapps.cn/*", "https://*excel.gov.online.office365.us/*", "https://*onenote.gov.online.office365.us/*", "https://*powerpoint.gov.online.office365.us/*", "https://*word-edit.gov.online.office365.us/*", "https://*excel.dod.online.office365.us/*", "https://*onenote.dod.online.office365.us/*", "https://*powerpoint.dod.online.office365.us/*", "https://*word-edit.dod.online.office365.us/*", "https://*visio.partner.officewebapps.cn/*", "https://*visio.gov.online.office365.us/*", "https://*visio.dod.online.office365.us/*"], "matches": ["https://*.officeapps.live.com/*", "https://*.officeapps.live.com.mcas.ms/*", "https://*.partner.officewebapps.cn/*", "https://*.gov.online.office365.us/*", "https://*.dod.online.office365.us/*", "https://*.app.whiteboard.microsoft.com/*", "https://*.whiteboard.office.com/*", "https://*.app.int.whiteboard.microsoft.com/*", "https://*.whiteboard.office365.us/*", "https://*.dev.whiteboard.microsoft.com/*"], "permissions": ["clipboardRead", "clipboardWrite"]}, "default_locale": "en", "description": "This extension grants Microsoft web sites permission to read and write from the clipboard.", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCz4t/X7GeuP6GBpjmxndrjtzF//4CWeHlC68rkoV7hP3h5Ka6eX7ZMNlYJkSjmB5iRmPHO5kR1y7rGY8JXnRPDQh/CQNLVA7OsKeV6w+UO+vx8KGI+TrTAhzH8YGcMIsxsUjxtC4cBmprja+xDr0zVp2EMgqHu+GBKgwSRHTkDuwIDAQAB", "manifest_version": 2, "minimum_chrome_version": "77", "name": "Microsoft Clipboard Extension", "version": "1.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\140.0.3485.14\\resources\\edge_clipboard", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "ehlmnljdoejdahfjdfobmpfancoibmig": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "ejefaeioamebhekmfaclajddbpnnobje": {"lastpingday": "*****************"}, "feaiphogcfmhkkbodjnmbjcbaeiikbbm": {"lastpingday": "*****************"}, "fikbjbembnmfhppjfnmfkahdhfohhjmg": {"account_extension_type": 0, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"persistent": false, "scripts": ["background.js"]}, "externally_connectable": {"matches": ["https://*.microsoftstream.com/*"]}, "incognito": "split", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAsAmDrYmQaYQlLxSAn/jTQTGNt1IffJGIJeKucE/B42d8QIyFD2RCarmHP1bmbY1YuTng2dL3J//qyvUNwXPt9cmxH9WKwi512tzOa5r2zYaCuOgP2vAIrah/bKnpO3XmUfFWj+LRcbZahOmMDMQxzPKxFKuIz2eOiakBXDE6Ok7azHJ13LLQTte1JgZIPmyFrAciPABLp/IKLfsfnebVW1YgaOyxBNyp/7bhSmoyZI3kBv8InKOpGE8pttrBg6l5zkvD67a7ViNAYkqZIpJJV5ZTQtVWCWSG0xU2y+3zXZtx8KbGbDiWUAcwNYDVPpsV+IQXVpgAplHvrZme+hAl6QIDAQAB", "manifest_version": 2, "name": "Media Internals Services Extension", "permissions": ["mediaInternalsPrivate"], "version": "2.0.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\140.0.3485.14\\resources\\media_internals_services", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "fjngpfnaikknjdhkckmncgicobbkcnle": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "gbihlnbpmfkodghomcinpblknjhneknc": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "gbmoeijgfngecijpcnbooedokgafmmji": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "gcinnojdebelpnodghnoicmcdmamjoch": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "gecfnmoodchdkebjjffmdcmeghkflpib": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "hfmgbegjielnmfghmoohgmplnpeehike": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "iglcjdemknebjbklcgkfaebgojjphkec": {"account_extension_type": 0, "active_permissions": {"api": ["identity", "management", "metricsPrivate", "webstorePrivate", "hubPrivate"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "w", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://microsoftedge.microsoft.com"}, "urls": ["https://microsoftedge.microsoft.com"]}, "description": "Des<PERSON>bra as extensões do Microsoft Edge.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtMvN4+y6cd3el/A/NT5eUnrz1WiD1WJRaJfMBvaMtJHIuFGEmYdYL/YuE74J19+pwhjOHeFZ3XUSMTdOa5moOaXXvdMr5wWaaN2frHewtAnNDO64NGbbZvdsfGm/kRkHKVGNV6dacZsAkylcz5CkwTmq97wOZ7ETaShHvhZEGwRQIt4K1poxurOkDYQw9ERZNf3fgYJ9ZTrLZMAFDLJY+uSF03pClWrr8VGc8LUQ4Naktb8QSgVUlrS14AdF/ESdbhnTvvdB0e7peNWRyoNtCqLJsbtTtBL6sOnqfusnwPowuueOFI+XskOT9TvLo6PcgxhLX5+d0mM+Jtn6PFTU8QIDAQAB", "name": "Microsoft Store", "permissions": ["webstorePrivate", "management", "metricsPrivate", "identity", "hubPrivate"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\140.0.3485.14\\resources\\microsoft_web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "ihmafllikibpmigkcoadcmckbfhibefp": {"account_extension_type": 0, "active_permissions": {"api": ["debugger", "feedbackPrivate", "fileSystem", "fileSystem.write", "app.window.fullscreen", "metricsPrivate", "storage", "tabs", "fileSystem.readFullPath", "edgeInternetConnectivityPrivate"], "explicit_host": ["edge://resources/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["edgeFeedbackPrivate.onFeedbackRequested"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"background": {"scripts": ["js/event_handler.js"]}, "content_security_policy": "default-src 'none'; script-src 'self' blob: filesystem: chrome://resources; style-src 'unsafe-inline' blob: chrome: file: filesystem: data: *; img-src * blob: chrome: file: filesystem: data:; media-src 'self' blob: filesystem:; connect-src data:"}, "description": "User feedback extension", "display_in_launcher": false, "display_in_new_tab_page": false, "icons": {"128": "images/icon128.png", "16": "images/icon16.png", "192": "images/icon192.png", "32": "images/icon32.png", "48": "images/icon48.png"}, "incognito": "split", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAl3vxWwvLjcMIFK4OfG6C8PmJkMhFYDKRnx+SqG23YlMG1A+bOkiNmAN1TWpFPPp1f2PpbiZGNq1y29u/QfkD+PC4bnO7GbNw/2X5tGoP0n2K+KGGAxhnr0ki/oyo2eiFGSTOXlQvTRo5q1vB+Lbg+9TbFsWKlHZyAkeZ/YGz/iijHTqw8Q4RWdl5Tp8SlUhS/92EsWhveNJLW22veaT/Up2iSeSSwfyoHVYy8LUPaD4fbyLvPQacVLJq1dac2bNDqjaNvSPgPWCnkZtDmawZrgxT53otLCES/e96xfAf8I24VHIc1pVP8LqdqKr1AV1Yxn93h3VJ2QejtEhIAWHU6QIDAQAB", "manifest_version": 2, "name": "<PERSON>", "permissions": ["chrome://resources/", "debugger", "edgeInternetConnectivityPrivate", "feedbackPrivate", {"fileSystem": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "write"]}, "fullscreen", "metricsPrivate", "storage", "windows"], "version": "*******"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\140.0.3485.14\\resources\\edge_feedback", "preferences": {}, "regular_only_preferences": {}, "running": false, "was_installed_by_default": false, "was_installed_by_oem": false}, "jbleckejnaboogigodiafflhkajdmpcl": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "jdiccldimpdaibmpdkjnbmckianbfold": {"account_extension_type": 0, "active_permissions": {"api": ["activeTab", "metricsPrivate", "storage", "systemPrivate", "ttsEngine", "errorReporting"], "explicit_host": ["https://*.bing.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["ttsEngine.onPause", "ttsEngine.onResume", "ttsEngine.onSpeak", "ttsEngine.onStop"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"persistent": false, "scripts": ["lifetimeHelper.js", "telemetryHelper.js", "errorHelper.js", "voiceList/voiceListRequester.js", "voiceList/voiceListSingleton.js", "voiceList/voiceModel.js", "manifestHelper.js", "config.js", "ssml.js", "uuid.js", "wordBoundary.js", "audioStreamer.js", "wordBoundaryEventManager.js", "audioViewModel.js", "background.js"]}, "description": "Provides access to Microsoft's online text-to-speech voices", "key": "AAAAB3NzaC1yc2EAAAADAQABAAAAgQDjGOAV6/3fmEtQmFqlmqm5cZ+jlNhd6XikwMDp0I7BKh+AjG3aBIG/qqwlsF/7LAGatnSxBwUwZC0qMnGXtcOPVl26Q8OvMx0gt5Va5gxca+ae0Skluj9WN9TNxPFVhw21WbCt4D9q3kb+XXDlx/7v1ktYus4Fwr/skkjADG9cIQ==", "manifest_version": 2, "name": "Microsoft Voices", "permissions": ["activeTab", "errorReporting", "metricsPrivate", "storage", "systemPrivate", "ttsEngine", "https://*.bing.com/"], "tts_engine": {"voices": [{"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-US", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-US, AriaNeural)", "voice_name": "Microsoft Aria Online (Natural) - English (United States)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-US", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-US, GuyNeural)", "voice_name": "Microsoft Guy Online (Natural) - English (United States)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "zh-CN", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (zh<PERSON><PERSON><PERSON>, XiaoxiaoNeural)", "voice_name": "Microsoft Xiaoxiao Online (Natural) - Chinese (Mainland)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "zh-CN", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (zh-CN, YunyangNeural)", "voice_name": "Microsoft Yunyang Online (Natural) - Chinese (Mainland)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "zh-TW", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (zh-TW, HanHanRUS)", "voice_name": "Microsoft HanHan Online - Chinese (Taiwan)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "zh-HK", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (zh-HK, TracyRUS)", "voice_name": "Microsoft Tracy Online - Chinese (Hong Kong)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "ja-<PERSON>", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON><PERSON>, NanamiNeural)", "voice_name": "Microsoft Nanami Online (Natural) - Japanese (Japan)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-GB", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-GB, LibbyNeural)", "voice_name": "Microsoft Libby Online (Natural) - English (United Kingdom)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "pt-BR", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (pt-BR, FranciscaNeural)", "voice_name": "Microsoft Francisca Online (Natural) - Portuguese (Brazil)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "es-MX", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (es-MX, DaliaNeural)", "voice_name": "Microsoft Dalia Online (Natural) - Spanish (Mexico)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-IN", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-IN, PriyaRUS)", "voice_name": "Microsoft Priya Online - English (India)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-CA", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-CA, HeatherRUS)", "voice_name": "Microsoft Heather Online - English (Canada)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "fr-CA", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (fr-CA, SylvieNeural)", "voice_name": "Microsoft Sylvie Online (Natural) - French (Canada)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "fr-FR", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (fr-<PERSON><PERSON>, <PERSON>)", "voice_name": "Microsoft Denise Online (Natural) - French (France)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "de-DE", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (de-DE, KatjaNeural)", "voice_name": "Microsoft Katja Online (Natural) - German (Germany)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "ru-RU", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (ru-RU, EkaterinaRUS)", "voice_name": "Microsoft Ekaterina Online - Russian (Russia)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-AU", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-AU, HayleyRUS)", "voice_name": "Microsoft Hayley Online - English (Australia)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "it-IT", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (it-IT, ElsaNeural)", "voice_name": "Microsoft Elsa Online (Natural) - Italian (Italy)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "ko-KR", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (ko-KR, SunHiNeural)", "voice_name": "Microsoft SunHi Online (Natural) - Korean (Korea)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "nl-NL", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (nl-NL, HannaRUS)", "voice_name": "Microsoft Hanna Online - Dutch (Netherlands)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "es-ES", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (es-ES, ElviraNeural)", "voice_name": "Microsoft Elvira Online (Natural) - Spanish (Spain)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "tr-TR", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (tr-TR, EmelNeural)", "voice_name": "Microsoft Emel Online (Natural) - Turkish (Turkey)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "pl-PL", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (pl-PL, PaulinaRUS)", "voice_name": "Microsoft Paulina Online - Polish (Poland)"}]}, "version": "1.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\140.0.3485.14\\resources\\microsoft_voices", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "kagpabjoboikccfdghpdlaaopmgpgfdc": {"lastpingday": "*****************"}, "kfihiegbjaloebkmglnjnljoljgkkchm": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "llhcnbijpnechllogkacbcjmkcgjbjfi": {"lastpingday": "*****************"}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate", "fileSystem.readFullPath", "errorReporting", "edgeLearningToolsPrivate", "fileSystem.getCurrentEntry", "edgePdfPrivate", "edgeCertVerifierPrivate"], "explicit_host": ["edge://resources/*", "edge://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:; trusted-types edge-internal fast-html pdf-url edge-pdf-static-policy;", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "edge_pdf/index.html", "name": "Microsoft Edge PDF Viewer", "offline_enabled": true, "permissions": ["errorReporting", "chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "edgeCertVerifierPrivate", "edgeLearningToolsPrivate", "edgePdfPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getCurrentEntry"]}], "version": "1"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\140.0.3485.14\\resources\\edge_pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "ncbjelpjchkpbikbpkcchkhkblodoama": {"account_extension_type": 0, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"persistent": false, "scripts": ["background.js"]}, "externally_connectable": {"matches": ["https://*.teams.microsoft.com/*", "https://*.skype.com/*", "https://*.teams.live.com/*"]}, "incognito": "split", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtAdFAR3ckd5c7G8VSzUj4Ltt/QRInUOD00StG95LweksGcLBlFlYL46cHFVgHHj1gmzcpBtgsURdcrAC3V8yiE7GY4wtpOP+9l+adUGR+cyOG0mw9fLjyH+2Il0QqktsNXzkNiE1ogW4l0h4+PJc262j0vtm4hBzMvR0QScFWcAIcAErlUiWTt4jefXCAYqubV99ed5MvVMWBxe97wOa9hYwAhbCminOepA4RRTg9eyi0TiuHpq/bNI8C5qZgKIQNBAjgiFBaIx9hiMBFlK4NHUbFdgY6Qp/hSCMNurctwz1jpsXEnT4eHg1YWXfquoH8s4swIjkFCMBF6Ejc3cUkQIDAQAB", "manifest_version": 2, "name": "WebRTC Internals Extension", "permissions": ["webrtcInternalsPrivate"], "version": "2.0.2"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\140.0.3485.14\\resources\\webrtc_internals", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "ndcileolkflehcjpmjnfbnaibdcgglog": {"lastpingday": "*****************"}, "njjljiblognghfjfpcdpdbpbfcmhgafg": {"lastpingday": "*****************"}, "nkbndigcebkoaejohleckhekfmcecfja": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "nkeimhogjdpnpccoofpliimaahmaaome": {"account_extension_type": 0, "active_permissions": {"api": ["processes", "webrtcLoggingPrivate", "system.cpu", "enterprise.hardwarePlatform"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["runtime.onConnectExternal"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"page": "background.html", "persistent": false}, "externally_connectable": {"matches": ["https://*.teams.microsoft.com/*", "https://*.teams.live.com/*", "https://*.skype.com/*"]}, "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDAQt2ZDdPfoSe/JI6ID5bgLHRCnCu9T36aYczmhw/tnv6QZB2I6WnOCMZXJZlRdqWc7w9jo4BWhYS50Vb4weMfh/I0On7VcRwJUgfAxW2cHB+EkmtI1v4v/OU24OqIa1Nmv9uRVeX0GjhQukdLNhAE6ACWooaf5kqKlCeK+1GOkQIDAQAB", "manifest_version": 2, "name": "WebRTC Extension", "permissions": ["enterprise.hardwarePlatform", "processes", "system.cpu", "webrtcLoggingPrivate"], "version": "2.3.24"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\140.0.3485.14\\resources\\hangout_services", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "nlbmdekgjkajiobkcbpolefohlelfhfe": {"lastpingday": "*****************"}, "ofefcgjbeghpigppfmkologfjadafddi": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}}}, "google": {"services": {"last_signed_in_username": "<EMAIL>"}}, "homepage": "http://www.google.com.br/", "homepage_is_newtabpage": true, "protection": {"macs": {"browser": {"show_home_button": "AD488728007881E54A017AED167D4EE021A44BE39182D15A459FF742C4C2FCBB"}, "default_search_provider_data": {"template_url_data": "14EABC7531565D830D75F3BCFCECE0B53ACE524F62F5E0183C8EF549BCFFC82D"}, "edge": {"services": {"account_id": "1DFC060CCC4589D1B41C8E10E4E906FEAFBAD1ECC3F7CBCE2C5F71296F379D3E", "last_username": "4BAB708D17261B8D71EB3960885169592E80F27BB9A5FB6AF6A9FDA47C0833B4"}}, "enterprise_signin": {"policy_recovery_token": "8F32BF7D1DD530E011EE9027545EFA60A4B913A8CACC80652DCCA29B5CE8B997"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "E17D41A582CD80447DE070F23EDCE093D021DFBE73FEF977A834BE675A1CA4D7", "bmgogkgklcedkeplmalbcklcbpgelpdg": "7695813B68BECC2119BDE1993FD085AE9241948BEB685BAC821E596496A5007D", "cjneempfhkonkkbcmnfdibgobmhbagaj": "E1041207A246C2CCBB2BA55C471A89B19B8BE5A2E4FE301825A02C2C8B79C6D4", "ckcbfnpodigdcbjjmhmolhkhlfbepnca": "2F2776EF2D250B9B7174760935C061B09A6FB4072A4637D155439DAECD8DED34", "dcaajljecejllikfgbhjdgeognacjkkp": "A29D1E96DAFE532A030B30C68A6E53A37A08F79D2221AC41FDE19A8F97619845", "dgiklkfkllikcanfonkcabmbdfmgleag": "A641C1C557BE0105764786760F8178C608B5B00A172DB7B391DA36A9CFF3D786", "ehlmnljdoejdahfjdfobmpfancoibmig": "11A90EBA438A5CA5B3D87ABE1FBFCA956859E40BEF53D75DDC9E80DFB7A23D4C", "ejefaeioamebhekmfaclajddbpnnobje": "0874AB83EF79B7C9FA9A8E0D1551074A39212708F156E26351D2B8DD06FA37AA", "feaiphogcfmhkkbodjnmbjcbaeiikbbm": "29440C0888FC49B13DAF52A46B1124B9C85361BF4C6F249BC58717DB68589796", "fikbjbembnmfhppjfnmfkahdhfohhjmg": "DEE8DD7F94FC48EE038480BF1400B3EA90F2562EF51D6620D993F86197090134", "fjngpfnaikknjdhkckmncgicobbkcnle": "C8660D631E4082CE3F4B7DD7E9FE691070B0B7D567500C9DA3463E745A7728E8", "gbihlnbpmfkodghomcinpblknjhneknc": "142D801C47FA4F0384A9133D45A69D3FA9EC772E1EBDF933619BD0FFCC6510DC", "gbmoeijgfngecijpcnbooedokgafmmji": "1D8B003FE5239AA302B78AD916393A9336D94309663E6E949EE3671087875772", "gcinnojdebelpnodghnoicmcdmamjoch": "C698363B88F02E9F92A860A9CAE22774DF7D8D6FE3FEA1B35C47C1F164D7ED58", "gecfnmoodchdkebjjffmdcmeghkflpib": "990D0024961006EF3221E79116A0249FFEBB61B3803978179A138AD87061686D", "hfmgbegjielnmfghmoohgmplnpeehike": "8CA7A3697354E5B723B8B9B15AA1F5F0FEF0A8D2BCBF1C222DD860274B3D274A", "iglcjdemknebjbklcgkfaebgojjphkec": "040538DD61604CE1C312B39EDF935BA11E9B146A61E22F49AD051607830A40DC", "ihmafllikibpmigkcoadcmckbfhibefp": "EC7750D06A4275BF471493DC52802EF88142F85CB895B496F58BFA03F281513E", "jbleckejnaboogigodiafflhkajdmpcl": "81369E6F07CB515BEB089D5CA0398EDCDF777167431A23111F58F4BA39FBFD8A", "jdiccldimpdaibmpdkjnbmckianbfold": "572467B050B76174DAC49B44AA80D56163897FCF314E641A9C4E7D77C91E8D27", "kagpabjoboikccfdghpdlaaopmgpgfdc": "AE7CC288E0256D40996759C93A5A21D027E5F7DA44EB69D80BC2B960AEB2262D", "kfihiegbjaloebkmglnjnljoljgkkchm": "2B865CA61C477DE72B8CC1CFA3026C5002F73B95DFB46F54410E916C494447AB", "llhcnbijpnechllogkacbcjmkcgjbjfi": "828DCE57E3EBB15F2470D630E19166831BFEBD9498C333985C20A47E4C8574D6", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "C3CB4E1BF2BB18B1B0EA8EA75976A5B7F51E524C7E1BAECB40E0F2ACF98F8A00", "ncbjelpjchkpbikbpkcchkhkblodoama": "093678DA1B32AE484DA823FCA753E6690E72F85740C797EF5D5AC06425E0910A", "ndcileolkflehcjpmjnfbnaibdcgglog": "E768C9ABF268EC5C8D67C1201CE76CE8E7FE0B3245ECA8A48D56E014E9899D28", "njjljiblognghfjfpcdpdbpbfcmhgafg": "06A909834ABF0D767FF6F4DCF20EC3780EC72CEB5015E311E5F5D98DD7B53D98", "nkbndigcebkoaejohleckhekfmcecfja": "63554351013E438BBD1F69CAC43624A99F5FE31B9BF9BD0C3F7CCA7607E42A74", "nkeimhogjdpnpccoofpliimaahmaaome": "8E55F041230D61577B3BB8DF46B52B1E82452AB292E6209333B6E18AD9265616", "nlbmdekgjkajiobkcbpolefohlelfhfe": "35C6D53CF029CFF01F53F40D73910821C90F4B3F09981312C18FBD9286DAE4FF", "ofefcgjbeghpigppfmkologfjadafddi": "02D3936AC3B3A81575C7B7FD307B19FE651CDE5513DD0B00ECFB34005BC69314"}, "ui": {"developer_mode": "F1A9A5CFB1A69935EB98255171E9C799EE49EFF832BA073E33FE0ECD3C5844BF"}}, "google": {"services": {"last_signed_in_username": "C34D7D2AC30B6C1BBDC3CA5A28AEE6130B3249F265FEC3BE2C2BE7005807A0FD"}}, "homepage": "E9B971698DDF2CBB043A738069FC53D537BF5BF9FE70D0903E296571E6D20BC3", "homepage_is_newtabpage": "BC18A2C9B3C8FAAD6369EA251040666ED15ED5E8C72468AFE797A1B8746D949D", "media": {"cdm": {"origin_data": "F1152C376A3F8CBED52232D93408997FDFCE2B4874AB7921822BB14824C3EFCF"}, "storage_id_salt": "111CEA848490E4664EB4C2CD813E47E4FD8883D90E4455767ADF874F5EA1EC54"}, "pinned_tabs": "9F2B95B63ED21EC3181BF8A64D3E3210A930FE6BE48B71EBEB30F33D2EC62977", "prefs": {"preference_reset_time": "A7FC47AA083ABF049F81253286165B742EAB8B65CAA12A9CA79ABD6BB152B758"}, "safebrowsing": {"incidents_sent": "42E658D4D2F225DBD53BC233708CF4CFE09495F82FFE8ED1C94E9444553BD534"}, "schedule_to_flush_to_disk": "54E20C41396F4181C6AB89DC450C734372ED535E13BF5DB146D2003F14F6D616", "search_provider_overrides": "35ED844370900963C4E865E1EE98C87C1F1463E1DE0894E738611A801B1208D9", "session": {"restore_on_startup": "293DA88517A05871CE2ED66DDB345967D1A8C217C564C5CF4BF49C2908893CFE", "startup_urls": "D521BA4EDC344413FFA4331378E746B262324E22EE8EF721E7F302E6C4605E7E"}}, "super_mac": "79BC719C8ED599206FB0FFC67C1F2123570D8A1B8133A790E57E5D5243942F3F"}, "session": {"restore_on_startup": 1, "restore_on_startup_edge_enclave": "E90000000100000001010000020000008677C590E86E165A39B9D6249756446BDC2F6A7AAAD9FF70D764B76E24D909DF977C8C1E530235855B662893394EF2AB0300000000000000F8A051C46271405EBBB8F49F69AC02740B7BD647EC8249A09EFDC59CD22498895994F30058CF2F220DB479F3E5BAF96B6D3CD1B6E1712264E092955FB2ADCC621106B773B40572F7D02A0CFF794B00E10E1331BBA27AB0619DB8187376744A5C407C7E1F251E4312B56F1161F9E856625A46FC4386A840E5BCAAC98F066A0A02650000000000000002000000000000000000000010000000050000007ECB7715C1", "restore_on_startup_edge_enclave_verify": "3e27816dee47c930695ac7d53bf62029", "startup_urls": ["http://search.babylon.com/?affID=110819&tt=060612_6_&babsrc=HP_ss&mntrId=02f83607000000000000d0df9a497059", "http://start.funmoods.com/?f=1&a=ironpub&chnl=ironpub&cd=2XzuyEtN2Y1L1Qzu0DtD0D0Fzy0AyEzyyBtDyDzytAyCtDyBtN0D0Tzu0StByEyDtN1L2XzutBtFtCtFtCtFtAtCtB&cr=1841451143", "http://search.conduit.com/?ctid=CT3027459&SearchSource=48", "http://www.searchnu.com/102", "http://feed.snapdo.com/?publisher=AirInstallerND&dpid=AirInstallerND&co=BR&userid=32b309e2-0138-8a85-d1db-0135e8d5a288&searchtype=hp&installDate={installDate}", "http://feed.snapdo.com/?publisher=AirInstallerND&dpid=AirInstallerND&co=BR&userid=32b309e2-0138-8a85-d1db-0135e8d5a288&searchtype=hp&installDate=12/11/2013", "http://www.search.ask.com/?o=APN11459&gct=hp&d=488-210&v=a13277-351&t=4", "http://www.mystartsearch.com/?type=hp&ts=1413899066&from=amt&uid=ST750LM022XHN-M750MBB_S317J90DB08133B08133", "http://www.amisites.com/?type=hp&ts=1480330584&z=23f932a292124b76276aab4gaz7b9e5c2cce8w9b8o&from=archer1028&uid=KINGSTONXSV300S37A240G_50026B7254019872"], "startup_urls_edge_enclave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startup_urls_edge_enclave_verify": "22c84015fd76f055b4094fcb5b5ec57f"}}