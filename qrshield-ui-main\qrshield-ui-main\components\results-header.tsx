import { ArrowLeft, RotateCcw } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"

export function ResultsHeader() {
  return (
    <header className="bg-card border-b border-border">
      <div className="container mx-auto px-4 py-4 max-w-md">
        <div className="flex items-center justify-between">
          <Button variant="ghost" size="sm" className="p-2">
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="font-serif font-bold text-lg text-foreground">Análise de Segurança</h1>
          <Button variant="ghost" size="sm" className="p-2">
            <RotateCcw className="h-5 w-5" />
          </Button>
        </div>
      </div>
    </header>
  )
}
