import { Shield, ChevronDown, Lightbulb } from "lucide-react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"

interface SecurityExplanationProps {
  isSecure: boolean
}

export function SecurityExplanation({ isSecure }: SecurityExplanationProps) {
  return (
    <div className="space-y-4">
      <Collapsible>
        <Card>
          <CollapsibleTrigger className="w-full">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center justify-between text-base font-medium">
                <div className="flex items-center gap-2">
                  <Shield className="h-4 w-4 text-green-600" />
                  Por que isso é {isSecure ? "seguro" : "arriscado"}?
                </div>
                <ChevronDown className="h-4 w-4 text-muted-foreground" />
              </CardTitle>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent className="pt-0">
              <p className="text-sm text-muted-foreground leading-relaxed">
                {isSecure
                  ? "Este link foi verificado e não apresenta sinais de atividade maliciosa. O domínio é conhecido e confiável, sem histórico de phishing ou malware."
                  : "Este link apresenta características suspeitas que podem indicar tentativa de phishing, malware ou outras atividades maliciosas."}
              </p>
            </CardContent>
          </CollapsibleContent>
        </Card>
      </Collapsible>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-base font-medium">
            <Lightbulb className="h-4 w-4 text-amber-600" />O que posso fazer agora?
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="space-y-2 text-sm text-muted-foreground">
            <li className="flex items-start gap-2">
              <span className="text-accent font-bold">•</span>
              <span>Copie o link para verificar em outro lugar</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-accent font-bold">•</span>
              <span>{isSecure ? "Abra o link com segurança" : "Evite abrir links suspeitos"}</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-accent font-bold">•</span>
              <span>Denuncie se suspeitar de atividade maliciosa</span>
            </li>
          </ul>
        </CardContent>
      </Card>
    </div>
  )
}
