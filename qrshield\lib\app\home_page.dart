import 'package:flutter/material.dart';
import 'package:qrshield/app/router.dart';
import 'package:qrshield/core/services/image_qr_scanner.dart';

/// Clean and modern home page following the reference design
class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: AppBar(
        backgroundColor: colorScheme.surface,
        elevation: 0,
        title: Text(
          'QRShield',
          style: textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.w900,
            color: colorScheme.onSurface,
          ),
        ),
        actions: [
          IconButton(
            onPressed: () => context.goSettings(),
            icon: Icon(
              Icons.settings_outlined,
              color: colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            tooltip: 'Configurações',
          ),
        ],
      ),
      body: Safe<PERSON>rea(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 400),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
            child: Column(
              children: [
                // Scanner Section
                _ScannerSection(),

                const SizedBox(height: 24),

                // Security Tips Section
                _SecurityTipsSection(),

                const SizedBox(height: 24),

                // How it works button
                _HowItWorksButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Scanner section widget following the reference design
class _ScannerSection extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Column(
      children: [
        // QR Code Icon with modern styling
        Container(
          width: 96,
          height: 96,
          decoration: BoxDecoration(
            color: colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: colorScheme.shadow.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Icon(
            Icons.qr_code_2_rounded,
            size: 48,
            color: colorScheme.primary,
          ),
        ),

        const SizedBox(height: 32),

        // Main Heading
        Text(
          'Analise QR Codes com\nsegurança antes de abrir',
          style: textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.w900,
            color: colorScheme.onSurface,
            height: 1.2,
          ),
          textAlign: TextAlign.center,
        ),

        const SizedBox(height: 12),

        // Subtitle
        Text(
          'Proteja-se contra golpes e links maliciosos',
          style: textTheme.bodyLarge?.copyWith(
            color: colorScheme.onSurface.withValues(alpha: 0.7),
          ),
          textAlign: TextAlign.center,
        ),

        const SizedBox(height: 32),

        // Primary Action Button
        SizedBox(
          width: double.infinity,
          child: FilledButton.icon(
            onPressed: () => context.goScan(),
            icon: const Icon(Icons.camera_alt_outlined),
            label: const Text('Escanear agora'),
            style: FilledButton.styleFrom(
              backgroundColor: colorScheme.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              textStyle: textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),

        const SizedBox(height: 16),

        // Secondary Action Button
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: () => _showImageSourceDialog(context),
            icon: const Icon(Icons.upload_outlined),
            label: const Text('Enviar imagem'),
            style: OutlinedButton.styleFrom(
              foregroundColor: colorScheme.onSurface,
              side: BorderSide(color: colorScheme.outline),
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              backgroundColor: Colors.transparent,
            ),
          ),
        ),
      ],
    );
  }

  /// Show dialog to choose image source (gallery or camera)
  void _showImageSourceDialog(BuildContext context) {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Selecionar imagem'),
        content: const Text('Escolha a origem da imagem para análise:'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancelar'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _scanFromGallery(context);
            },
            child: const Text('Galeria'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _scanFromCamera(context);
            },
            child: const Text('Câmera'),
          ),
        ],
      ),
    );
  }

  /// Scan QR code from gallery image
  Future<void> _scanFromGallery(BuildContext context) async {
    try {
      _showLoadingDialog(context, 'Processando imagem...');

      final result = await ImageQRScanner.scanFromGallery();

      if (context.mounted) {
        Navigator.pop(context); // Close loading dialog

        if (result != null) {
          context.goReport(result);
        } else {
          _showErrorSnackBar(context, 'Seleção de imagem cancelada');
        }
      }
    } catch (e) {
      if (context.mounted) {
        Navigator.pop(context); // Close loading dialog
        _showErrorSnackBar(context, e.toString());
      }
    }
  }

  /// Scan QR code from camera image
  Future<void> _scanFromCamera(BuildContext context) async {
    try {
      _showLoadingDialog(context, 'Processando imagem...');

      final result = await ImageQRScanner.scanFromCamera();

      if (context.mounted) {
        Navigator.pop(context); // Close loading dialog

        if (result != null) {
          context.goReport(result);
        } else {
          _showErrorSnackBar(context, 'Captura de imagem cancelada');
        }
      }
    } catch (e) {
      if (context.mounted) {
        Navigator.pop(context); // Close loading dialog
        _showErrorSnackBar(context, e.toString());
      }
    }
  }

  /// Show loading dialog
  void _showLoadingDialog(BuildContext context, String message) {
    showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: 16),
            Expanded(child: Text(message)),
          ],
        ),
      ),
    );
  }

  /// Show error snackbar
  void _showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.error,
      ),
    );
  }
}

/// Security tips section following the reference design
class _SecurityTipsSection extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Dicas de segurança',
          style: textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w900,
            color: colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 16),

        const _SecurityTipCard(
          icon: Icons.warning_amber_rounded,
          title: 'Nunca abra links automaticamente',
          description: 'Sempre analise o destino antes de abrir qualquer QR Code.',
          color: Color(0xFFD97706),
        ),
        const SizedBox(height: 12),

        const _SecurityTipCard(
          icon: Icons.shield_outlined,
          title: 'Verifique a origem',
          description: 'Confirme se o QR Code vem de uma fonte confiável.',
          color: Color(0xFF6366F1),
        ),
        const SizedBox(height: 12),

        const _SecurityTipCard(
          icon: Icons.visibility_outlined,
          title: 'Examine URLs suspeitas',
          description: 'Desconfie de links encurtados ou domínios estranhos.',
          color: Color(0xFFD97706),
        ),
      ],
    );
  }
}

/// How it works button following the reference design
class _HowItWorksButton extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Center(
      child: TextButton.icon(
        onPressed: () => context.goSettings(),
        icon: Icon(
          Icons.help_outline_rounded,
          color: colorScheme.onSurface.withValues(alpha: 0.7),
        ),
        label: Text(
          'Como funciona',
          style: TextStyle(
            color: colorScheme.onSurface.withValues(alpha: 0.7),
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}

/// Security tip card widget matching the design
class _SecurityTipCard extends StatelessWidget {
  const _SecurityTipCard({
    required this.icon,
    required this.title,
    required this.description,
    required this.color,
  });

  final IconData icon;
  final String title;
  final String description;
  final Color color;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
