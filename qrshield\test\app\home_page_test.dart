import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:qrshield/app/home_page.dart';

void main() {
  Widget createTestWidget() {
    return const ProviderScope(
      child: MaterialApp(
        home: Scaffold(
          body: SingleChildScrollView(
            child: Si<PERSON>B<PERSON>(
              height: 1200, // Provide enough height for content
              child: HomePage(),
            ),
          ),
        ),
      ),
    );
  }

  group('HomePage Widget Tests', () {

    testWidgets('should display QRShield title in app bar', (tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      expect(find.text('QRShield'), findsOneWidget);
    });

    testWidgets('should display settings button in app bar', (tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      expect(find.byIcon(Icons.settings_outlined), findsOneWidget);
    });

    testWidgets('should display QR code icon', (tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      expect(find.byIcon(Icons.qr_code_2_rounded), findsOneWidget);
    });

    testWidgets('should display main title and subtitle', (tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      expect(find.text('Analise QR Codes com\nsegurança antes de abrir'), findsOneWidget);
      expect(find.text('Proteja-se contra golpes e links maliciosos'), findsOneWidget);
    });

    testWidgets('should display scan now button', (tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      expect(find.text('Escanear agora'), findsOneWidget);
      expect(find.byIcon(Icons.camera_alt_outlined), findsOneWidget);
    });

    testWidgets('should display upload image button', (tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      expect(find.text('Enviar imagem'), findsOneWidget);
      expect(find.byIcon(Icons.upload_outlined), findsOneWidget);
    });

    testWidgets('should display security tips section', (tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      expect(find.text('Dicas de segurança'), findsOneWidget);
      expect(find.text('Nunca abra links automaticamente'), findsOneWidget);
      expect(find.text('Verifique a origem'), findsOneWidget);
      expect(find.text('Examine URLs suspeitas'), findsOneWidget);
    });

    testWidgets('should display how it works button', (tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      expect(find.text('Como funciona'), findsOneWidget);
      expect(find.byIcon(Icons.help_outline_rounded), findsOneWidget);
    });

    testWidgets('should have scan button with correct properties', (tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      final scanButton = find.text('Escanear agora');
      expect(scanButton, findsOneWidget);

      // Verify button exists and is a FilledButton
      final buttonWidget = tester.widget<FilledButton>(
        find.ancestor(
          of: scanButton,
          matching: find.byType(FilledButton),
        ),
      );
      expect(buttonWidget.onPressed, isNotNull);
    });

    testWidgets('should have settings button with correct properties', (tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      final settingsButton = find.byIcon(Icons.settings_outlined);
      expect(settingsButton, findsOneWidget);

      // Verify button exists and is an IconButton
      final buttonWidget = tester.widget<IconButton>(
        find.ancestor(
          of: settingsButton,
          matching: find.byType(IconButton),
        ),
      );
      expect(buttonWidget.onPressed, isNotNull);
    });

    testWidgets('should have upload button with correct properties', (tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      final uploadButton = find.text('Enviar imagem');
      expect(uploadButton, findsOneWidget);

      // Verify button exists and is an OutlinedButton
      final buttonWidget = tester.widget<OutlinedButton>(
        find.ancestor(
          of: uploadButton,
          matching: find.byType(OutlinedButton),
        ),
      );
      expect(buttonWidget.onPressed, isNotNull);
    });
  });

  group('HomePage Layout Tests', () {
    testWidgets('should display all security tip cards', (tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Check for security tip icons
      expect(find.byIcon(Icons.warning_amber_rounded), findsOneWidget);
      expect(find.byIcon(Icons.shield_outlined), findsOneWidget);
      expect(find.byIcon(Icons.visibility_outlined), findsOneWidget);
    });

    testWidgets('should have scrollable content', (tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Verify that SingleChildScrollView is present
      expect(find.byType(SingleChildScrollView), findsOneWidget);
    });
  });
}
