"use client"

import { useState } from "react"
import { QrCode, Camera, Upload } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"

export function ScannerSection() {
  const [isScanning, setIsScanning] = useState(false)

  const handleScanClick = () => {
    setIsScanning(true)
    // Simulate scanning process
    setTimeout(() => setIsScanning(false), 3000)
  }

  return (
    <section className="text-center py-8">
      {/* QR Code Icon with Animation */}
      <div className="relative mb-8">
        <div className="w-24 h-24 mx-auto bg-card rounded-2xl flex items-center justify-center shadow-lg">
          <QrCode className="w-12 h-12 text-accent" />
        </div>
        {isScanning && (
          <div className="absolute inset-0 w-24 h-24 mx-auto border-2 border-accent rounded-2xl pulse-ring" />
        )}
      </div>

      {/* Main Heading */}
      <h2 className="text-2xl font-serif font-black text-foreground mb-3 leading-tight">
        Analise QR Codes com segurança antes de abrir
      </h2>

      {/* Subtitle */}
      <p className="text-muted-foreground mb-8 text-base leading-relaxed">
        Proteja-se contra golpes e links maliciosos
      </p>

      {/* Primary Action Button */}
      <Button
        onClick={handleScanClick}
        disabled={isScanning}
        className="w-full bg-accent hover:bg-accent/90 text-accent-foreground font-semibold py-4 text-lg rounded-xl shadow-lg transition-all duration-200 hover:shadow-xl hover:scale-[1.02] mb-4"
      >
        <Camera className="w-5 h-5 mr-2" />
        {isScanning ? "Escaneando..." : "Escanear agora"}
      </Button>

      {/* Secondary Action */}
      <Button
        variant="outline"
        className="w-full border-border text-card-foreground hover:bg-muted py-3 rounded-xl bg-transparent"
      >
        <Upload className="w-4 h-4 mr-2" />
        Enviar imagem
      </Button>

      {/* Scanning Feedback */}
      {isScanning && (
        <Card className="mt-6 p-4 bg-accent/5 border-accent/20">
          <div className="flex items-center justify-center space-x-2">
            <div className="w-2 h-2 bg-accent rounded-full animate-pulse" />
            <p className="text-accent font-medium">Analisando código QR...</p>
          </div>
        </Card>
      )}
    </section>
  )
}
