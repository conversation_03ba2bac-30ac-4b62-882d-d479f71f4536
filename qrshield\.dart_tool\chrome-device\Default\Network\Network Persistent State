{"net": {"http_server_properties": {"broken_alternative_services": [{"anonymization": ["FAAAABAAAABodHRwczovL2JpbmcuY29t", false, 0], "broken_count": 2, "broken_until": "1755911563", "host": "www.bing.com", "port": 443, "protocol_str": "quic"}], "servers": [{"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13402976857432096", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "server": "https://www.gstatic.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL21pY3Jvc29mdC5jb20AAAA=", false, 0], "server": "https://prod.rewardsplatform.microsoft.com", "supports_spdy": true}, {"anonymization": ["LAAAACYAAABodHRwczovL2VkZ2Vhc3NldHNlcnZpY2UuYXp1cmVlZGdlLm5ldAAA", false, 0], "server": "https://edgeassetservice.azureedge.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13400478466757339", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL2JpbmcuY29t", false, 0], "server": "https://www.bing.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13402976876704506", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://apps.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13402976877596078", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://fonts.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13402976877773722", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://storage.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13402976878751953", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://analytics.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13402976878768268", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://stats.g.doubleclick.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13402976878931667", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://www.google-analytics.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13402976879449458", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://ssl.gstatic.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL21pY3Jvc29mdC5jb20AAAA=", false, 0], "server": "https://edge.microsoft.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13400471257182695", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "network_stats": {"srtt": 31097}, "server": "https://unpkg.com"}, {"anonymization": ["GAAAABQAAABodHRwczovL2JpbmdhcGlzLmNvbQ==", false, 0], "server": "https://www.bingapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13402976859357944", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL29mZmljZS5jb20AAA==", false, 0], "network_stats": {"srtt": 26040}, "server": "https://substrate.office.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13402976879146393", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", true, 0], "network_stats": {"srtt": 15694}, "server": "https://2507573.fls.doubleclick.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13402976879026799", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 16083}, "server": "https://ad.doubleclick.net"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13402976879316097", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", true, 0], "network_stats": {"srtt": 15464}, "server": "https://adservice.google.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13402976876565485", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 15395}, "server": "https://clients2.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13402976878993940", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL2dvb2dsZXVzZXJjb250ZW50LmNvbQAAAA==", false, 0], "network_stats": {"srtt": 18476}, "server": "https://clients2.googleusercontent.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13402976872638008", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "network_stats": {"srtt": 15629}, "server": "https://fonts.gstatic.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13402976877780208", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 16478}, "server": "https://fonts.gstatic.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13402976879236837", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 16656}, "server": "https://googleads.g.doubleclick.net"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13402976877716270", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 18550}, "server": "https://lh3.googleusercontent.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13402976876408592", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 17327}, "server": "https://meet.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13402976878297280", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 17021}, "server": "https://ssl.google-analytics.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13402976879020651", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 16924}, "server": "https://workspace.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13402976879314405", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 18403}, "server": "https://www.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13402976879323789", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 16591}, "server": "https://www.google.com.br"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13402976879411364", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 18577}, "server": "https://www.googletagmanager.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13402976878469648", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", true, 0], "network_stats": {"srtt": 32554}, "server": "https://www.googletagmanager.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13402976879412928", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 17230}, "server": "https://www.gstatic.com", "supports_spdy": true}], "supports_quic": {"address": "2804:3d90:829d:19b1:17d0:5276:8a4a:afaf", "used_quic": true}, "version": 5}, "network_qualities": {"CAESABiAgICA+P////8B": "4G"}}}