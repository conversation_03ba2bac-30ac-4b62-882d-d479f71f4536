import { Search } from "lucide-react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"

interface ScanResultsProps {
  data: {
    type: string
    url: string
    domain: string
    path: string
  }
}

export function ScanResults({ data }: ScanResultsProps) {
  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-base font-medium">
          <Search className="h-4 w-4 text-muted-foreground" />O que encontramos
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="grid grid-cols-[80px_1fr] gap-3 text-sm">
          <span className="text-muted-foreground">Tipo</span>
          <span className="font-medium">{data.type}</span>
        </div>
        <div className="grid grid-cols-[80px_1fr] gap-3 text-sm">
          <span className="text-muted-foreground">URL</span>
          <span className="font-mono text-xs break-all">{data.url}</span>
        </div>
        <div className="grid grid-cols-[80px_1fr] gap-3 text-sm">
          <span className="text-muted-foreground">Domínio</span>
          <span className="font-medium">{data.domain}</span>
        </div>
        <div className="grid grid-cols-[80px_1fr] gap-3 text-sm">
          <span className="text-muted-foreground">Caminho</span>
          <span className="font-mono text-xs">{data.path}</span>
        </div>
      </CardContent>
    </Card>
  )
}
