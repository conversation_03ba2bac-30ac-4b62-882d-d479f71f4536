import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Eye } from "lucide-react"
import { Card } from "@/components/ui/card"

const tips = [
  {
    icon: AlertTriangle,
    title: "Nunca abra links automaticamente",
    description: "Sempre analise o destino antes de abrir qualquer QR Code.",
    color: "text-destructive",
    bgColor: "bg-destructive/10",
  },
  {
    icon: Shield,
    title: "Verifique a origem",
    description: "Confirme se o QR Code vem de uma fonte confiável.",
    color: "text-accent",
    bgColor: "bg-accent/10",
  },
  {
    icon: Eye,
    title: "Examine URLs suspeitas",
    description: "Desconfie de links encurtados ou domínios estranhos.",
    color: "text-chart-2",
    bgColor: "bg-chart-2/10",
  },
]

export function SecurityTips() {
  return (
    <section className="py-6">
      <h3 className="text-lg font-serif font-bold text-foreground mb-4">Dicas de segurança</h3>

      <div className="space-y-3">
        {tips.map((tip, index) => (
          <Card key={index} className="p-4 bg-card border-border hover:shadow-md transition-shadow duration-200">
            <div className="flex items-start space-x-3">
              <div className={`p-2 rounded-lg ${tip.bgColor} flex-shrink-0`}>
                <tip.icon className={`w-5 h-5 ${tip.color}`} />
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="font-semibold text-card-foreground text-sm mb-1">{tip.title}</h4>
                <p className="text-muted-foreground text-sm leading-relaxed">{tip.description}</p>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </section>
  )
}
